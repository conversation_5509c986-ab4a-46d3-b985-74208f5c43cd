@echo off
chcp 65001 >nul
echo [+] 使用MinGW64编译无模块DLL注入工具
echo ================================

:: 设置MinGW64路径
set MINGW_BIN=C:\mingw64\bin
set GCC=%MINGW_BIN%\gcc.exe
set GPP=%MINGW_BIN%\g++.exe

:: 检查MinGW64是否存在
if not exist "%GPP%" (
    echo [-] 未找到MinGW64编译器: %GPP%
    echo [!] 请确保MinGW64已安装在 C:\mingw64
    pause
    exit /b 1
)

echo [+] 找到编译器: %GPP%

:: 创建输出目录
if not exist "bin" mkdir bin

echo [+] 编译注入器...
"%GPP%" -std=c++11 -O2 -static -o bin/Injector.exe Injector.cpp ManualMapper.cpp -lkernel32 -luser32 -ladvapi32
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败
    pause
    exit /b 1
)

echo [+] 编译被注入DLL...
"%GPP%" -std=c++11 -O2 -shared -static -o bin/Payload.dll Payload.cpp -lkernel32 -luser32 -Wl,--enable-stdcall-fixup
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    pause
    exit /b 1
)

echo [+] 编译测试目标...
"%GPP%" -std=c++11 -O2 -static -o bin/TestTarget.exe test_target.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    pause
    exit /b 1
)

echo [+] 编译控制器...
"%GPP%" -std=c++11 -O2 -static -o bin/Controller.exe Controller.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    pause
    exit /b 1
)

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
echo 使用方法:
echo   1. bin\Injector.exe ^<进程名^> bin\Payload.dll
echo   2. bin\Controller.exe
echo.
pause
