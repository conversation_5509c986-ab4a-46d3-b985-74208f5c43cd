# Visual Studio 2022 编译脚本
Write-Host "[+] Visual Studio 2022 编译脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 设置Visual Studio路径
$VSPath = "F:\Visual Studio2022"
$MSVCVersion = "14.44.35207"
$MSVCPath = "$VSPath\VC\Tools\MSVC\$MSVCVersion"

# 检查编译器
$CompilerPath = "$MSVCPath\bin\Hostx64\x64\cl.exe"
if (!(Test-Path $CompilerPath)) {
    Write-Host "[-] 未找到Visual Studio编译器: $CompilerPath" -ForegroundColor Red
    exit 1
}

Write-Host "[+] 找到Visual Studio编译器" -ForegroundColor Green

# 设置环境变量
$env:PATH = "$MSVCPath\bin\Hostx64\x64;$env:PATH"

# 设置包含目录
$IncludePaths = @(
    "$MSVCPath\include"
)

# 检查Windows SDK
$SDKPath = "C:\Program Files (x86)\Windows Kits\10"
if (Test-Path $SDKPath) {
    $SDKVersions = Get-ChildItem "$SDKPath\Include" | Sort-Object Name -Descending
    if ($SDKVersions.Count -gt 0) {
        $SDKVersion = $SDKVersions[0].Name
        Write-Host "[+] 找到Windows SDK: $SDKVersion" -ForegroundColor Green
        $IncludePaths += "$SDKPath\Include\$SDKVersion\um"
        $IncludePaths += "$SDKPath\Include\$SDKVersion\shared"
        $IncludePaths += "$SDKPath\Include\$SDKVersion\ucrt"
        
        $LibPaths = @(
            "$MSVCPath\lib\x64"
            "$SDKPath\Lib\$SDKVersion\um\x64"
            "$SDKPath\Lib\$SDKVersion\ucrt\x64"
        )
    }
}

$env:INCLUDE = ($IncludePaths -join ";") + ";$env:INCLUDE"
$env:LIB = ($LibPaths -join ";") + ";$env:LIB"

# 创建输出目录
if (!(Test-Path "bin")) {
    New-Item -ItemType Directory -Name "bin" | Out-Null
}

Write-Host "[+] 开始编译..." -ForegroundColor Green

# 编译函数
function Compile-File {
    param($Name, $Sources, $Output, $Type = "exe", $ExtraLibs = "")
    
    Write-Host "[+] 编译 $Name..." -ForegroundColor Yellow
    
    $Args = @("/EHsc", "/O2", "/MT")
    if ($Type -eq "dll") {
        $Args += "/LD"
    }
    $Args += "/Fe:$Output"
    $Args += $Sources
    $Args += "/link"
    $Args += "kernel32.lib", "user32.lib"
    if ($ExtraLibs) {
        $Args += $ExtraLibs
    }
    if ($Type -eq "exe") {
        $Args += "/SUBSYSTEM:CONSOLE"
    }
    
    & $CompilerPath $Args
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[+] $Name 编译成功" -ForegroundColor Green
        return $true
    } else {
        Write-Host "[-] $Name 编译失败" -ForegroundColor Red
        return $false
    }
}

# 编译各个组件
$Success = $true

$Success = $Success -and (Compile-File "测试目标" "test_target.cpp" "bin\TestTarget.exe")
$Success = $Success -and (Compile-File "被注入DLL" "Payload.cpp" "bin\Payload.dll" "dll")
$Success = $Success -and (Compile-File "控制器" "Controller.cpp" "bin\Controller.exe")
$Success = $Success -and (Compile-File "简单注入器" @("SimpleInjector.cpp", "ManualMapper.cpp") "bin\SimpleInjector.exe" "exe" "advapi32.lib")

# 尝试编译原始注入器
if (Test-Path "Injector.cpp") {
    Compile-File "原始注入器" @("Injector.cpp", "ManualMapper.cpp") "bin\Injector.exe" "exe" "advapi32.lib" | Out-Null
}

# 清理临时文件
Get-ChildItem -Filter "*.obj" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Filter "*.exp" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Filter "*.lib" | Remove-Item -Force -ErrorAction SilentlyContinue
Get-ChildItem -Filter "*.pdb" | Remove-Item -Force -ErrorAction SilentlyContinue

if ($Success) {
    Write-Host "[+] 编译完成！" -ForegroundColor Green
    Write-Host "[+] 输出文件位于 bin\ 目录" -ForegroundColor Green
    Write-Host ""
    Get-ChildItem "bin" | Format-Table Name, Length, LastWriteTime
    Write-Host ""
    Write-Host "使用方法:" -ForegroundColor Cyan
    Write-Host "  1. bin\TestTarget.exe" -ForegroundColor White
    Write-Host "  2. bin\SimpleInjector.exe <进程名> bin\Payload.dll" -ForegroundColor White
    Write-Host "  3. bin\Controller.exe" -ForegroundColor White
} else {
    Write-Host "[-] 编译过程中出现错误" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
