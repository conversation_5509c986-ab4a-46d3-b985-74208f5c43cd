@echo off
chcp 65001 >nul
echo [+] 编译环境配置脚本
echo ================================

echo [+] 检查现有编译器...

:: 检查TDM-GCC
if exist "C:\TDM-GCC-64\bin\gcc.exe" (
    echo [+] 找到TDM-GCC-64编译器
    set COMPILER_PATH=C:\TDM-GCC-64\bin
    set COMPILER_NAME=TDM-GCC-64
    goto :configure_env
)

:: 检查MinGW-w64源码目录
if exist "C:\mingw-w64-v13.0.0" (
    echo [!] 发现MinGW-w64源码目录，但这需要编译
    echo [!] 建议使用预编译版本
)

:: 检查系统PATH中的gcc
where gcc >nul 2>&1
if %errorlevel% equ 0 (
    echo [+] 系统PATH中找到GCC编译器
    for /f "tokens=*" %%i in ('where gcc') do (
        echo [+] GCC路径: %%i
        set COMPILER_PATH=%%~dpi
        set COMPILER_NAME=系统GCC
        goto :configure_env
    )
)

echo [-] 未找到可用的C++编译器
echo.
echo 建议安装选项:
echo 1. 使用现有的TDM-GCC-64 (推荐)
echo 2. 下载预编译的MinGW-w64
echo 3. 安装Visual Studio Community
echo.
goto :end

:configure_env
echo [+] 使用编译器: %COMPILER_NAME%
echo [+] 编译器路径: %COMPILER_PATH%

:: 测试编译器
echo [+] 测试编译器...
"%COMPILER_PATH%\gcc.exe" --version
if %errorlevel% neq 0 (
    echo [-] 编译器测试失败
    goto :end
)

echo [+] 测试G++编译器...
"%COMPILER_PATH%\g++.exe" --version
if %errorlevel% neq 0 (
    echo [-] G++编译器测试失败
    goto :end
)

:: 创建编译脚本
echo [+] 创建项目编译脚本...

echo @echo off > build_project.bat
echo chcp 65001 ^>nul >> build_project.bat
echo echo [+] 使用 %COMPILER_NAME% 编译项目 >> build_project.bat
echo echo ================================ >> build_project.bat
echo. >> build_project.bat
echo :: 创建输出目录 >> build_project.bat
echo if not exist "bin" mkdir bin >> build_project.bat
echo. >> build_project.bat
echo echo [+] 编译测试目标... >> build_project.bat
echo "%COMPILER_PATH%\g++.exe" -std=c++11 -O2 -static -o bin/TestTarget.exe test_target.cpp -lkernel32 -luser32 >> build_project.bat
echo if %%errorlevel%% neq 0 goto :error >> build_project.bat
echo. >> build_project.bat
echo echo [+] 编译被注入DLL... >> build_project.bat
echo "%COMPILER_PATH%\g++.exe" -std=c++11 -O2 -shared -o bin/Payload.dll Payload.cpp -lkernel32 -luser32 >> build_project.bat
echo if %%errorlevel%% neq 0 goto :error >> build_project.bat
echo. >> build_project.bat
echo echo [+] 编译控制器... >> build_project.bat
echo "%COMPILER_PATH%\g++.exe" -std=c++11 -O2 -static -o bin/Controller.exe Controller.cpp -lkernel32 -luser32 >> build_project.bat
echo if %%errorlevel%% neq 0 goto :error >> build_project.bat
echo. >> build_project.bat
echo echo [+] 编译注入器... >> build_project.bat
echo "%COMPILER_PATH%\g++.exe" -std=c++11 -O2 -static -o bin/SimpleInjector.exe SimpleInjector.cpp ManualMapper.cpp -lkernel32 -luser32 -ladvapi32 >> build_project.bat
echo if %%errorlevel%% neq 0 goto :error >> build_project.bat
echo. >> build_project.bat
echo echo [+] 编译完成！ >> build_project.bat
echo echo [+] 输出文件位于 bin\ 目录 >> build_project.bat
echo dir bin >> build_project.bat
echo goto :end >> build_project.bat
echo. >> build_project.bat
echo :error >> build_project.bat
echo echo [-] 编译失败 >> build_project.bat
echo pause >> build_project.bat
echo exit /b 1 >> build_project.bat
echo. >> build_project.bat
echo :end >> build_project.bat
echo pause >> build_project.bat

echo [+] 编译脚本已创建: build_project.bat

:: 检查是否需要添加到PATH
echo %PATH% | findstr /i "%COMPILER_PATH%" >nul
if %errorlevel% neq 0 (
    echo [!] 编译器路径不在系统PATH中
    echo [!] 建议添加到PATH: %COMPILER_PATH%
    echo.
    echo 是否要添加到当前会话的PATH? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        set PATH=%PATH%;%COMPILER_PATH%
        echo [+] 已添加到当前会话PATH
    )
)

echo.
echo [+] 环境配置完成！
echo [+] 现在可以运行 build_project.bat 来编译项目
echo.

:end
pause
