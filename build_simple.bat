@echo off
chcp 65001 >nul
echo [+] 使用Visual Studio 2022编译项目
echo ================================

:: 设置Visual Studio环境
call "F:\Visual Studio2022\Common7\Tools\VsDevCmd.bat" -arch=x64

echo [+] Visual Studio环境已设置
echo [+] 开始编译...

:: 创建输出目录
if not exist "bin" mkdir bin

echo [+] 编译测试目标...
cl /EHsc /O2 /MT /Fe:bin\TestTarget.exe test_target.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    pause
    exit /b 1
)

echo [+] 编译被注入DLL...
cl /EHsc /O2 /MT /LD /Fe:bin\Payload.dll Payload.cpp /link kernel32.lib user32.lib
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    pause
    exit /b 1
)

echo [+] 编译控制器...
cl /EHsc /O2 /MT /Fe:bin\Controller.exe Controller.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    pause
    exit /b 1
)

echo [+] 编译注入器...
cl /EHsc /O2 /MT /Fe:bin\Injector.exe Injector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败，尝试编译简化版本...
    cl /EHsc /O2 /MT /Fe:bin\SimpleInjector.exe SimpleInjector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE
    if %errorlevel% neq 0 (
        echo [-] 简化注入器编译也失败
        pause
        exit /b 1
    )
    echo [+] 简化注入器编译成功
)

:: 清理临时文件
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1
del *.pdb >nul 2>&1

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
dir bin
echo.
echo 使用方法:
echo   1. bin\TestTarget.exe
echo   2. bin\Injector.exe ^<进程名^> bin\Payload.dll  
echo   3. bin\Controller.exe
echo.
pause
