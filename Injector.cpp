#include "ManualMapper.h"
#include <TlHelp32.h>
#include <iostream>
#include <string>

class ProcessHelper
{
public:
    static DWORD GetProcessIdByName(const std::wstring& processName)
    {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
            return 0;

        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);

        if (Process32FirstW(hSnapshot, &pe32))
        {
            do
            {
                if (processName == pe32.szExeFile)
                {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }

    static bool EnableDebugPrivilege()
    {
        HANDLE hToken;
        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken))
            return false;

        TOKEN_PRIVILEGES tp;
        tp.PrivilegeCount = 1;
        tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

        if (!LookupPrivilegeValueA(nullptr, SE_DEBUG_NAME, &tp.Privileges[0].Luid))
        {
            CloseHandle(hToken);
            return false;
        }

        bool result = AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(TOKEN_PRIVILEGES), nullptr, nullptr);
        CloseHandle(hToken);
        return result;
    }
};

void PrintUsage()
{
    std::wcout << L"用法: Injector.exe <进程名/PID> <DLL路径>" << std::endl;
    std::wcout << L"示例: " << std::endl;
    std::wcout << L"  Injector.exe notepad.exe payload.dll" << std::endl;
    std::wcout << L"  Injector.exe 1234 payload.dll" << std::endl;
}

int main(int argc, char* argv[])
{
    std::wcout << L"[+] 无模块DLL注入器 v1.0" << std::endl;
    std::wcout << L"[+] 作者: AI助手" << std::endl;
    std::wcout << L"================================" << std::endl;

    if (argc != 3)
    {
        PrintUsage();
        return 1;
    }

    // 提升权限
    if (!ProcessHelper::EnableDebugPrivilege())
    {
        std::wcout << L"[-] 无法获取调试权限" << std::endl;
        return 1;
    }
    std::wcout << L"[+] 成功获取调试权限" << std::endl;

    // 获取目标进程ID
    DWORD targetPID = 0;
    std::wstring target = argv[1];
    
    // 检查是否为数字(PID)
    bool isNumber = true;
    for (wchar_t c : target)
    {
        if (!iswdigit(c))
        {
            isNumber = false;
            break;
        }
    }

    if (isNumber)
    {
        targetPID = std::wcstoul(target.c_str(), nullptr, 10);
    }
    else
    {
        targetPID = ProcessHelper::GetProcessIdByName(target);
        if (targetPID == 0)
        {
            std::wcout << L"[-] 无法找到进程: " << target << std::endl;
            return 1;
        }
    }

    std::wcout << L"[+] 目标进程PID: " << targetPID << std::endl;

    // 打开目标进程
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPID);
    if (!hProcess)
    {
        std::wcout << L"[-] 无法打开目标进程, 错误代码: " << GetLastError() << std::endl;
        return 1;
    }
    std::wcout << L"[+] 成功打开目标进程" << std::endl;

    // 转换DLL路径为字符串
    std::string dllPath;
    int len = WideCharToMultiByte(CP_UTF8, 0, argv[2], -1, nullptr, 0, nullptr, nullptr);
    if (len > 0)
    {
        dllPath.resize(len - 1);
        WideCharToMultiByte(CP_UTF8, 0, argv[2], -1, &dllPath[0], len, nullptr, nullptr);
    }

    // 检查DLL文件是否存在
    DWORD fileAttr = GetFileAttributesA(dllPath.c_str());
    if (fileAttr == INVALID_FILE_ATTRIBUTES)
    {
        std::wcout << L"[-] DLL文件不存在: " << argv[2] << std::endl;
        CloseHandle(hProcess);
        return 1;
    }

    std::wcout << L"[+] 开始注入DLL: " << argv[2] << std::endl;

    // 执行注入
    if (ManualMapper::InjectDLL(hProcess, dllPath))
    {
        std::wcout << L"[+] DLL注入成功!" << std::endl;
        std::wcout << L"[+] 现在可以使用Controller.exe与注入的DLL通信" << std::endl;
    }
    else
    {
        std::wcout << L"[-] DLL注入失败!" << std::endl;
    }

    CloseHandle(hProcess);
    
    std::wcout << L"按任意键退出..." << std::endl;
    _getwch();
    return 0;
}