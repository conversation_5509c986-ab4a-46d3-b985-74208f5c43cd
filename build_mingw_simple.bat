@echo off
echo [+] Building with MinGW64...
echo ================================

:: Set MinGW64 path
set MINGW_BIN=C:\mingw64\bin
set GPP=%MINGW_BIN%\g++.exe

:: Check if MinGW64 exists
if not exist "%GPP%" (
    echo [-] MinGW64 compiler not found: %GPP%
    echo [!] Please ensure MinGW64 is installed at C:\mingw64
    pause
    exit /b 1
)

echo [+] Found compiler: %GPP%

:: Create output directory
if not exist "bin" mkdir bin

echo [+] Compiling TestTarget...
"%GPP%" -std=c++11 -O2 -static -o bin/TestTarget.exe test_target.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] TestTarget compilation failed
    pause
    exit /b 1
) else (
    echo [+] TestTarget compiled successfully
)

echo [+] Compiling Controller...
"%GPP%" -std=c++11 -O2 -static -o bin/Controller.exe Controller.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] Controller compilation failed
    pause
    exit /b 1
) else (
    echo [+] Controller compiled successfully
)

echo [+] Compiling Payload DLL...
"%GPP%" -std=c++11 -O2 -shared -o bin/Payload.dll Payload.cpp -lkernel32 -luser32 -Wl,--enable-stdcall-fixup
if %errorlevel% neq 0 (
    echo [-] DLL compilation failed
    pause
    exit /b 1
) else (
    echo [+] Payload DLL compiled successfully
)

echo [+] Compiling Injector...
"%GPP%" -std=c++11 -O2 -static -o bin/Injector.exe Injector.cpp ManualMapper.cpp -lkernel32 -luser32 -ladvapi32
if %errorlevel% neq 0 (
    echo [-] Injector compilation failed
    pause
    exit /b 1
) else (
    echo [+] Injector compiled successfully
)

echo.
echo [+] Build completed successfully!
echo [+] Output files are in bin\ directory
echo.
dir bin
echo.
echo Usage:
echo   1. bin\Injector.exe ^<process_name^> bin\Payload.dll
echo   2. bin\Controller.exe
echo.
pause
