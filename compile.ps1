Write-Host "[+] Visual Studio 2022 Compile Script" -ForegroundColor Green

# VS paths
$VSPath = "F:\Visual Studio2022"
$CompilerPath = "$VSPath\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe"

# Check compiler
if (!(Test-Path $CompilerPath)) {
    Write-Host "[-] Compiler not found: $CompilerPath" -ForegroundColor Red
    exit 1
}

# Create bin directory
if (!(Test-Path "bin")) {
    New-Item -ItemType Directory -Name "bin" | Out-Null
}

# Set environment
$env:PATH = "$VSPath\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64;$env:PATH"

# Find Windows SDK
$SDKPath = "C:\Program Files (x86)\Windows Kits\10"
$SDKVersion = ""
if (Test-Path $SDKPath) {
    $SDKVersions = Get-ChildItem "$SDKPath\Include" | Sort-Object Name -Descending
    if ($SDKVersions.Count -gt 0) {
        $SDKVersion = $SDKVersions[0].Name
        Write-Host "[+] Found Windows SDK: $SDKVersion" -ForegroundColor Green
    }
}

# Set include and lib paths
$IncludePaths = @(
    "$VSPath\VC\Tools\MSVC\14.44.35207\include"
)

$LibPaths = @(
    "$VSPath\VC\Tools\MSVC\14.44.35207\lib\x64"
)

if ($SDKVersion) {
    $IncludePaths += "$SDKPath\Include\$SDKVersion\um"
    $IncludePaths += "$SDKPath\Include\$SDKVersion\shared" 
    $IncludePaths += "$SDKPath\Include\$SDKVersion\ucrt"
    $LibPaths += "$SDKPath\Lib\$SDKVersion\um\x64"
    $LibPaths += "$SDKPath\Lib\$SDKVersion\ucrt\x64"
}

$env:INCLUDE = ($IncludePaths -join ";")
$env:LIB = ($LibPaths -join ";")

Write-Host "[+] Starting compilation..." -ForegroundColor Green

# Compile TestTarget
Write-Host "[+] Compiling TestTarget..." -ForegroundColor Yellow
& $CompilerPath /EHsc /O2 /MT /Fe:bin\TestTarget.exe test_target.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE

# Compile Payload DLL
Write-Host "[+] Compiling Payload DLL..." -ForegroundColor Yellow  
& $CompilerPath /EHsc /O2 /MT /LD /Fe:bin\Payload.dll Payload.cpp /link kernel32.lib user32.lib

# Compile Controller
Write-Host "[+] Compiling Controller..." -ForegroundColor Yellow
& $CompilerPath /EHsc /O2 /MT /Fe:bin\Controller.exe Controller.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE

# Compile SimpleInjector
Write-Host "[+] Compiling SimpleInjector..." -ForegroundColor Yellow
& $CompilerPath /EHsc /O2 /MT /Fe:bin\SimpleInjector.exe SimpleInjector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE

# Clean up
Remove-Item *.obj -ErrorAction SilentlyContinue
Remove-Item *.exp -ErrorAction SilentlyContinue  
Remove-Item *.lib -ErrorAction SilentlyContinue
Remove-Item *.pdb -ErrorAction SilentlyContinue

Write-Host "[+] Compilation completed!" -ForegroundColor Green
Get-ChildItem bin

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
