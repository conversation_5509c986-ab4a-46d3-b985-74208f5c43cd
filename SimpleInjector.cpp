#include "ManualMapper.h"
#include <TlHelp32.h>
#include <iostream>
#include <string>

class ProcessHelper
{
public:
    static DWORD GetProcessIdByName(const std::string& processName)
    {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
            return 0;

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32))
        {
            do
            {
                if (processName == pe32.szExeFile)
                {
                    CloseHandle(hSnapshot);
                    return pe32.th32ProcessID;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
        return 0;
    }

    static bool EnableDebugPrivilege()
    {
        HANDLE hToken;
        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken))
            return false;

        TOKEN_PRIVILEGES tp;
        tp.PrivilegeCount = 1;
        tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

        if (!LookupPrivilegeValueA(nullptr, SE_DEBUG_NAME, &tp.Privileges[0].Luid))
        {
            CloseHandle(hToken);
            return false;
        }

        bool result = AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(TOKEN_PRIVILEGES), nullptr, nullptr);
        CloseHandle(hToken);
        return result && GetLastError() == ERROR_SUCCESS;
    }
};

void PrintUsage()
{
    std::cout << "用法:" << std::endl;
    std::cout << "  SimpleInjector.exe <进程名或PID> <DLL路径>" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  SimpleInjector.exe notepad.exe payload.dll" << std::endl;
    std::cout << "  SimpleInjector.exe 1234 payload.dll" << std::endl;
}

int main(int argc, char* argv[])
{
    std::cout << "[+] 简单DLL注入器 v1.0" << std::endl;
    std::cout << "================================" << std::endl;

    if (argc != 3)
    {
        PrintUsage();
        return 1;
    }

    // 提升权限
    if (!ProcessHelper::EnableDebugPrivilege())
    {
        std::cout << "[-] 无法获取调试权限" << std::endl;
        return 1;
    }
    std::cout << "[+] 成功获取调试权限" << std::endl;

    // 获取目标进程ID
    DWORD targetPID = 0;
    std::string target = argv[1];
    
    // 检查是否为数字(PID)
    bool isNumber = true;
    for (char c : target)
    {
        if (!isdigit(c))
        {
            isNumber = false;
            break;
        }
    }

    if (isNumber)
    {
        targetPID = std::stoul(target);
        std::cout << "[+] 使用进程ID: " << targetPID << std::endl;
    }
    else
    {
        targetPID = ProcessHelper::GetProcessIdByName(target);
        if (targetPID == 0)
        {
            std::cout << "[-] 未找到进程: " << target << std::endl;
            return 1;
        }
        std::cout << "[+] 找到进程 " << target << " (PID: " << targetPID << ")" << std::endl;
    }

    // 打开目标进程
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, targetPID);
    if (!hProcess)
    {
        std::cout << "[-] 无法打开进程 (错误代码: " << GetLastError() << ")" << std::endl;
        return 1;
    }
    std::cout << "[+] 成功打开目标进程" << std::endl;

    // 执行DLL注入
    std::string dllPath = argv[2];
    std::cout << "[+] 开始注入DLL: " << dllPath << std::endl;
    
    if (ManualMapper::InjectDLL(hProcess, dllPath))
    {
        std::cout << "[+] DLL注入成功!" << std::endl;
        std::cout << "[+] 现在可以运行Controller.exe来操作内存" << std::endl;
    }
    else
    {
        std::cout << "[-] DLL注入失败" << std::endl;
        CloseHandle(hProcess);
        return 1;
    }

    CloseHandle(hProcess);
    std::cout << "[+] 注入完成" << std::endl;
    return 0;
}
