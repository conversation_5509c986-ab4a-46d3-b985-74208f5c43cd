#include <Windows.h>
#include <iostream>
#include <string>
#include <iomanip>
#include <vector>
#include "ManualMapper.h"

class MemoryController
{
private:
    HANDLE m_hPipe;
    bool m_bConnected;

public:
    MemoryController() : m_hPipe(INVALID_HANDLE_VALUE), m_bConnected(false) {}
    
    ~MemoryController()
    {
        Disconnect();
    }

    bool Connect()
    {
        if (m_bConnected)
            return true;

        std::wcout << L"[+] 正在连接到注入的DLL..." << std::endl;

        // 尝试连接到命名管道
        for (int i = 0; i < 10; ++i)
        {
            m_hPipe = CreateFileW(
                PIPE_NAME,
                GENERIC_READ | GENERIC_WRITE,
                0,
                nullptr,
                OPEN_EXISTING,
                0,
                nullptr
            );

            if (m_hPipe != INVALID_HANDLE_VALUE)
            {
                m_bConnected = true;
                std::wcout << L"[+] 成功连接到DLL!" << std::endl;
                return true;
            }

            if (GetLastError() != ERROR_PIPE_BUSY)
            {
                std::wcout << L"[-] 连接失败，错误代码: " << GetLastError() << std::endl;
                Sleep(1000);
                continue;
            }

            if (!WaitNamedPipeW(PIPE_NAME, 2000))
            {
                std::wcout << L"[-] 等待管道超时" << std::endl;
                Sleep(1000);
                continue;
            }
        }

        std::wcout << L"[-] 无法连接到DLL，请确保已成功注入" << std::endl;
        return false;
    }

    void Disconnect()
    {
        if (m_hPipe != INVALID_HANDLE_VALUE)
        {
            CloseHandle(m_hPipe);
            m_hPipe = INVALID_HANDLE_VALUE;
        }
        m_bConnected = false;
    }

    bool SendCommand(const Command& cmd, Response& response)
    {
        if (!m_bConnected)
            return false;

        DWORD bytesWritten, bytesRead;

        if (!WriteFile(m_hPipe, &cmd, sizeof(Command), &bytesWritten, nullptr))
        {
            std::wcout << L"[-] 发送命令失败" << std::endl;
            return false;
        }

        if (!ReadFile(m_hPipe, &response, sizeof(Response), &bytesRead, nullptr))
        {
            std::wcout << L"[-] 读取响应失败" << std::endl;
            return false;
        }

        return true;
    }

    bool ReadMemory(DWORD_PTR address, SIZE_T size, std::vector<BYTE>& data)
    {
        Command cmd = {};
        cmd.type = CMD_READ_MEMORY;
        cmd.address = address;
        cmd.size = size;

        Response response;
        if (!SendCommand(cmd, response))
            return false;

        if (!response.success)
        {
            std::wcout << L"[-] 读取内存失败，错误代码: " << response.errorCode << std::endl;
            return false;
        }

        data.resize(response.dataSize);
        memcpy(data.data(), response.data, response.dataSize);
        return true;
    }

    bool WriteMemory(DWORD_PTR address, const std::vector<BYTE>& data)
    {
        if (data.size() > sizeof(Command::data))
        {
            std::wcout << L"[-] 数据太大，无法写入" << std::endl;
            return false;
        }

        Command cmd = {};
        cmd.type = CMD_WRITE_MEMORY;
        cmd.address = address;
        cmd.size = data.size();
        memcpy(cmd.data, data.data(), data.size());

        Response response;
        if (!SendCommand(cmd, response))
            return false;

        if (!response.success)
        {
            std::wcout << L"[-] 写入内存失败，错误代码: " << response.errorCode << std::endl;
            return false;
        }

        return true;
    }

    bool AllocateMemory(SIZE_T size, DWORD protection, DWORD_PTR& address)
    {
        Command cmd = {};
        cmd.type = CMD_ALLOCATE_MEMORY;
        cmd.size = size;
        cmd.protection = protection;

        Response response;
        if (!SendCommand(cmd, response))
            return false;

        if (!response.success)
        {
            std::wcout << L"[-] 分配内存失败，错误代码: " << response.errorCode << std::endl;
            return false;
        }

        address = *reinterpret_cast<DWORD_PTR*>(response.data);
        return true;
    }

    bool FreeMemory(DWORD_PTR address)
    {
        Command cmd = {};
        cmd.type = CMD_FREE_MEMORY;
        cmd.address = address;

        Response response;
        if (!SendCommand(cmd, response))
            return false;

        if (!response.success)
        {
            std::wcout << L"[-] 释放内存失败，错误代码: " << response.errorCode << std::endl;
            return false;
        }

        return true;
    }
};

void PrintMenu()
{
    std::wcout << L"\n=============== 内存操作控制台 ===============" << std::endl;
    std::wcout << L"1. 读取内存" << std::endl;
    std::wcout << L"2. 写入内存" << std::endl;
    std::wcout << L"3. 分配内存" << std::endl;
    std::wcout << L"4. 释放内存" << std::endl;
    std::wcout << L"5. 十六进制转储" << std::endl;
    std::wcout << L"0. 退出" << std::endl;
    std::wcout << L"=============================================" << std::endl;
    std::wcout << L"请选择操作: ";
}

void HexDump(const std::vector<BYTE>& data, DWORD_PTR baseAddress)
{
    const int bytesPerLine = 16;
    
    for (size_t i = 0; i < data.size(); i += bytesPerLine)
    {
        // 打印地址
        std::wcout << std::hex << std::setfill(L'0') << std::setw(8) 
                   << (baseAddress + i) << L": ";

        // 打印十六进制数据
        for (int j = 0; j < bytesPerLine; ++j)
        {
            if (i + j < data.size())
            {
                std::wcout << std::hex << std::setfill(L'0') << std::setw(2) 
                           << static_cast<int>(data[i + j]) << L" ";
            }
            else
            {
                std::wcout << L"   ";
            }
        }

        std::wcout << L" ";

        // 打印ASCII字符
        for (int j = 0; j < bytesPerLine && i + j < data.size(); ++j)
        {
            BYTE b = data[i + j];
            if (b >= 32 && b <= 126)
                std::wcout << static_cast<wchar_t>(b);
            else
                std::wcout << L".";
        }

        std::wcout << std::endl;
    }
}

int main()
{
    std::wcout << L"[+] 内存操作控制器 v1.0" << std::endl;
    std::wcout << L"[+] 用于与注入的DLL通信" << std::endl;

    MemoryController controller;
    if (!controller.Connect())
    {
        std::wcout << L"[-] 无法连接到注入的DLL" << std::endl;
        std::wcout << L"请确保已使用Injector.exe成功注入DLL" << std::endl;
        return 1;
    }

    int choice;
    while (true)
    {
        PrintMenu();
        std::wcin >> choice;

        switch (choice)
        {
        case 1: // 读取内存
            {
                DWORD_PTR address;
                SIZE_T size;
                std::wcout << L"输入地址 (十六进制): 0x";
                std::wcin >> std::hex >> address;
                std::wcout << L"输入大小: ";
                std::wcin >> std::dec >> size;

                std::vector<BYTE> data;
                if (controller.ReadMemory(address, size, data))
                {
                    std::wcout << L"[+] 成功读取 " << size << L" 字节" << std::endl;
                    HexDump(data, address);
                }
            }
            break;

        case 2: // 写入内存
            {
                DWORD_PTR address;
                std::string hexData;
                std::wcout << L"输入地址 (十六进制): 0x";
                std::wcin >> std::hex >> address;
                std::wcout << L"输入十六进制数据 (如: 41424344): ";
                std::cin >> hexData;

                std::vector<BYTE> data;
                for (size_t i = 0; i < hexData.length(); i += 2)
                {
                    std::string byteStr = hexData.substr(i, 2);
                    BYTE byte = static_cast<BYTE>(strtoul(byteStr.c_str(), nullptr, 16));
                    data.push_back(byte);
                }

                if (controller.WriteMemory(address, data))
                {
                    std::wcout << L"[+] 成功写入 " << data.size() << L" 字节" << std::endl;
                }
            }
            break;

        case 3: // 分配内存
            {
                SIZE_T size;
                DWORD protection;
                std::wcout << L"输入大小: ";
                std::wcin >> std::dec >> size;
                std::wcout << L"输入保护属性 (如: " << PAGE_EXECUTE_READWRITE << L"): ";
                std::wcin >> protection;

                DWORD_PTR address;
                if (controller.AllocateMemory(size, protection, address))
                {
                    std::wcout << L"[+] 成功分配内存，地址: 0x" << std::hex << address << std::endl;
                }
            }
            break;

        case 4: // 释放内存
            {
                DWORD_PTR address;
                std::wcout << L"输入地址 (十六进制): 0x";
                std::wcin >> std::hex >> address;

                if (controller.FreeMemory(address))
                {
                    std::wcout << L"[+] 成功释放内存" << std::endl;
                }
            }
            break;

        case 5: // 十六进制转储
            {
                DWORD_PTR address;
                SIZE_T size;
                std::wcout << L"输入起始地址 (十六进制): 0x";
                std::wcin >> std::hex >> address;
                std::wcout << L"输入转储大小: ";
                std::wcin >> std::dec >> size;

                std::vector<BYTE> data;
                if (controller.ReadMemory(address, size, data))
                {
                    std::wcout << L"\n十六进制转储:" << std::endl;
                    HexDump(data, address);
                }
            }
            break;

        case 0: // 退出
            std::wcout << L"[+] 正在退出..." << std::endl;
            return 0;

        default:
            std::wcout << L"[-] 无效选择" << std::endl;
            break;
        }
    }

    return 0;
}