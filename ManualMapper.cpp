#include "ManualMapper.h"
#include <TlHelp32.h>

bool ManualMapper::InjectDLL(HANDLE hProcess, const std::string& dllPath)
{
    std::vector<BYTE> rawData = ReadFileToMemory(dllPath);
    if (rawData.empty())
    {
        std::cout << "[-] 无法读取DLL文件: " << dllPath << std::endl;
        return false;
    }

    return MapDLL(hProcess, rawData.data(), rawData.size());
}

bool ManualMapper::MapDLL(HANDLE hProcess, BYTE* pSrcData, SIZE_T fileSize)
{
    IMAGE_DOS_HEADER* pDosHeader = reinterpret_cast<IMAGE_DOS_HEADER*>(pSrcData);
    if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE)
    {
        std::cout << "[-] 无效的DOS头" << std::endl;
        return false;
    }

    IMAGE_NT_HEADERS* pNtHeaders = reinterpret_cast<IMAGE_NT_HEADERS*>(pSrcData + pDosHeader->e_lfanew);
    if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE)
    {
        std::cout << "[-] 无效的NT头" << std::endl;
        return false;
    }

    // 在目标进程中分配内存
    LPVOID pTargetBase = VirtualAllocEx(hProcess, nullptr, pNtHeaders->OptionalHeader.SizeOfImage, 
                                       MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!pTargetBase)
    {
        std::cout << "[-] 无法在目标进程中分配内存" << std::endl;
        return false;
    }

    std::cout << "[+] 在目标进程中分配内存: 0x" << std::hex << pTargetBase << std::endl;

    // 准备映射数据结构
    MANUAL_MAPPING_DATA data = { 0 };
    data.ImageBase = pTargetBase;
    data.fnLoadLibraryA = LoadLibraryA;
    data.fnGetProcAddress = GetProcAddress;

    // 复制头部
    if (!WriteProcessMemory(hProcess, pTargetBase, pSrcData, pNtHeaders->OptionalHeader.SizeOfHeaders, nullptr))
    {
        std::cout << "[-] 无法写入头部" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        return false;
    }

    // 复制节
    IMAGE_SECTION_HEADER* pSectionHeader = IMAGE_FIRST_SECTION(pNtHeaders);
    for (WORD i = 0; i < pNtHeaders->FileHeader.NumberOfSections; ++i, ++pSectionHeader)
    {
        if (pSectionHeader->SizeOfRawData)
        {
            if (!WriteProcessMemory(hProcess, 
                                  static_cast<BYTE*>(pTargetBase) + pSectionHeader->VirtualAddress,
                                  pSrcData + pSectionHeader->PointerToRawData,
                                  pSectionHeader->SizeOfRawData, nullptr))
            {
                std::cout << "[-] 无法写入节: " << pSectionHeader->Name << std::endl;
                VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
                return false;
            }
        }
    }

    // 分配并写入映射数据
    LPVOID pMappingData = VirtualAllocEx(hProcess, nullptr, sizeof(MANUAL_MAPPING_DATA), 
                                        MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    if (!pMappingData)
    {
        std::cout << "[-] 无法分配映射数据内存" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        return false;
    }

    if (!WriteProcessMemory(hProcess, pMappingData, &data, sizeof(MANUAL_MAPPING_DATA), nullptr))
    {
        std::cout << "[-] 无法写入映射数据" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pMappingData, 0, MEM_RELEASE);
        return false;
    }

    // 分配并写入shellcode
    LPVOID pShellcode = VirtualAllocEx(hProcess, nullptr, 0x1000, 
                                      MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!pShellcode)
    {
        std::cout << "[-] 无法分配shellcode内存" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pMappingData, 0, MEM_RELEASE);
        return false;
    }

    if (!WriteProcessMemory(hProcess, pShellcode, reinterpret_cast<LPCVOID>(Shellcode), 0x1000, nullptr))
    {
        std::cout << "[-] 无法写入shellcode" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pMappingData, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pShellcode, 0, MEM_RELEASE);
        return false;
    }

    // 创建远程线程执行shellcode
    HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0, 
                                       reinterpret_cast<LPTHREAD_START_ROUTINE>(pShellcode),
                                       pMappingData, 0, nullptr);
    if (!hThread)
    {
        std::cout << "[-] 无法创建远程线程" << std::endl;
        VirtualFreeEx(hProcess, pTargetBase, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pMappingData, 0, MEM_RELEASE);
        VirtualFreeEx(hProcess, pShellcode, 0, MEM_RELEASE);
        return false;
    }

    std::cout << "[+] 等待注入完成..." << std::endl;
    WaitForSingleObject(hThread, INFINITE);

    // 清理
    CloseHandle(hThread);
    VirtualFreeEx(hProcess, pMappingData, 0, MEM_RELEASE);
    VirtualFreeEx(hProcess, pShellcode, 0, MEM_RELEASE);

    std::cout << "[+] DLL注入完成!" << std::endl;
    return true;
}

void __stdcall ManualMapper::Shellcode(MANUAL_MAPPING_DATA* pData)
{
    if (!pData)
        return;

    BYTE* pBase = reinterpret_cast<BYTE*>(pData->ImageBase);
    IMAGE_DOS_HEADER* pDosHeader = reinterpret_cast<IMAGE_DOS_HEADER*>(pBase);
    IMAGE_NT_HEADERS* pNtHeaders = reinterpret_cast<IMAGE_NT_HEADERS*>(pBase + pDosHeader->e_lfanew);

    // 处理导入表
    if (pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].Size)
    {
        IMAGE_IMPORT_DESCRIPTOR* pImportDesc = reinterpret_cast<IMAGE_IMPORT_DESCRIPTOR*>(
            pBase + pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].VirtualAddress);

        while (pImportDesc->Name)
        {
            char* szMod = reinterpret_cast<char*>(pBase + pImportDesc->Name);
            HMODULE hDll = pData->fnLoadLibraryA(szMod);

            ULONG_PTR* pThunkRef = reinterpret_cast<ULONG_PTR*>(pBase + pImportDesc->OriginalFirstThunk);
            ULONG_PTR* pFuncRef = reinterpret_cast<ULONG_PTR*>(pBase + pImportDesc->FirstThunk);

            if (!pThunkRef)
                pThunkRef = pFuncRef;

            for (; *pThunkRef; ++pThunkRef, ++pFuncRef)
            {
                if (IMAGE_SNAP_BY_ORDINAL(*pThunkRef))
                {
                    *pFuncRef = reinterpret_cast<ULONG_PTR>(pData->fnGetProcAddress(hDll, 
                        reinterpret_cast<char*>(*pThunkRef & 0xFFFF)));
                }
                else
                {
                    IMAGE_IMPORT_BY_NAME* pImport = reinterpret_cast<IMAGE_IMPORT_BY_NAME*>(pBase + *pThunkRef);
                    *pFuncRef = reinterpret_cast<ULONG_PTR>(pData->fnGetProcAddress(hDll, pImport->Name));
                }
            }
            ++pImportDesc;
        }
    }

    // 处理重定位
    if (pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC].Size)
    {
        DWORD_PTR deltaImageBase = reinterpret_cast<DWORD_PTR>(pBase) - pNtHeaders->OptionalHeader.ImageBase;
        IMAGE_BASE_RELOCATION* pRelocData = reinterpret_cast<IMAGE_BASE_RELOCATION*>(
            pBase + pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC].VirtualAddress);

        while (pRelocData->VirtualAddress)
        {
            UINT AmountOfEntries = (pRelocData->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / sizeof(WORD);
            WORD* pRelativeInfo = reinterpret_cast<WORD*>(pRelocData + 1);

            for (UINT i = 0; i != AmountOfEntries; ++i, ++pRelativeInfo)
            {
                if (RELOC_FLAG(*pRelativeInfo))
                {
                    UINT_PTR* pPatch = reinterpret_cast<UINT_PTR*>(pBase + pRelocData->VirtualAddress + ((*pRelativeInfo) & 0xFFF));
                    *pPatch += deltaImageBase;
                }
            }
            pRelocData = reinterpret_cast<IMAGE_BASE_RELOCATION*>(reinterpret_cast<BYTE*>(pRelocData) + pRelocData->SizeOfBlock);
        }
    }

    // 调用DllMain
    if (pNtHeaders->OptionalHeader.AddressOfEntryPoint)
    {
        pData->fnDllMain = reinterpret_cast<BOOL(WINAPI*)(HMODULE, DWORD, LPVOID)>(
            pBase + pNtHeaders->OptionalHeader.AddressOfEntryPoint);
        pData->fnDllMain(reinterpret_cast<HMODULE>(pBase), DLL_PROCESS_ATTACH, nullptr);
    }
}

std::vector<BYTE> ManualMapper::ReadFileToMemory(const std::string& filePath)
{
    std::ifstream file(filePath, std::ios::binary | std::ios::ate);
    if (!file.is_open())
        return {};

    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<BYTE> buffer(size);
    if (!file.read(reinterpret_cast<char*>(buffer.data()), size))
        return {};

    return buffer;
}