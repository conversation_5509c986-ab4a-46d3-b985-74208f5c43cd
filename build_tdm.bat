@echo off
chcp 65001 >nul
echo [+] 使用TDM-GCC编译无模块DLL注入工具
echo ================================

:: 检查TDM-GCC环境
if not exist "C:\TDM-GCC-64\bin\g++.exe" (
    echo [-] 未找到TDM-GCC编译器
    echo [!] 请确保TDM-GCC安装在 C:\TDM-GCC-64
    pause
    exit /b 1
)

:: 创建输出目录
if not exist "bin" mkdir bin

echo [+] 编译测试目标...
C:\TDM-GCC-64\bin\g++.exe -std=c++11 -O2 -static -o bin/TestTarget.exe test_target.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    pause
    exit /b 1
)

echo [+] 编译被注入DLL...
C:\TDM-GCC-64\bin\g++.exe -std=c++11 -O2 -shared -static -o bin/Payload.dll Payload.cpp -lkernel32 -luser32 -Wl,--enable-stdcall-fixup
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    pause
    exit /b 1
)

echo [+] 编译控制器...
C:\TDM-GCC-64\bin\g++.exe -std=c++11 -O2 -static -o bin/Controller.exe Controller.cpp -lkernel32 -luser32
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    pause
    exit /b 1
)

echo [+] 编译注入器...
C:\TDM-GCC-64\bin\g++.exe -std=c++11 -O2 -static -o bin/Injector.exe Injector.cpp ManualMapper.cpp -lkernel32 -luser32 -ladvapi32
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败，但其他组件编译成功
    echo [!] 可以尝试手动修复注入器代码
)

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
dir bin
echo.
pause
