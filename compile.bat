@echo off
echo [+] Visual Studio 2022 Compile Script
echo ================================

REM Set Visual Studio environment
call "F:\Visual Studio2022\Common7\Tools\VsDevCmd.bat"

echo [+] Starting compilation...

REM Create output directory
if not exist "bin" mkdir bin

echo [+] Compiling TestTarget...
cl /EHsc /O2 /MT /Fe:bin\TestTarget.exe test_target.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE

echo [+] Compiling Payload DLL...
cl /EHsc /O2 /MT /LD /Fe:bin\Payload.dll Payload.cpp /link kernel32.lib user32.lib

echo [+] Compiling Controller...
cl /EHsc /O2 /MT /Fe:bin\Controller.exe Controller.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE

echo [+] Compiling SimpleInjector...
cl /EHsc /O2 /MT /Fe:bin\SimpleInjector.exe SimpleInjector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE

REM Clean up
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1
del *.pdb >nul 2>&1

echo [+] Compilation completed!
dir bin
pause
