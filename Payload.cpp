#include <Windows.h>
#include <iostream>
#include <thread>
#include "ManualMapper.h"

// 全局变量
HANDLE g_hPipe = INVALID_HANDLE_VALUE;
bool g_bRunning = false;
std::thread g_communicationThread;

// 内存操作函数
BOOL ReadTargetMemory(DWORD_PTR address, LPVOID buffer, SIZE_T size)
{
    // 简化版本，不使用SEH
    if (IsBadReadPtr(reinterpret_cast<LPCVOID>(address), size))
        return FALSE;

    memcpy(buffer, reinterpret_cast<LPCVOID>(address), size);
    return TRUE;
}

BOOL WriteTargetMemory(DWORD_PTR address, LPCVOID buffer, SIZE_T size)
{
    DWORD oldProtect;
    if (!VirtualProtect(reinterpret_cast<LPVOID>(address), size, PAGE_EXECUTE_READWRITE, &oldProtect))
        return FALSE;

    // 简化版本，不使用SEH
    if (IsBadWritePtr(reinterpret_cast<LPVOID>(address), size))
    {
        VirtualProtect(reinterpret_cast<LPVOID>(address), size, oldProtect, &oldProtect);
        return FALSE;
    }

    memcpy(reinterpret_cast<LPVOID>(address), buffer, size);
    VirtualProtect(reinterpret_cast<LPVOID>(address), size, oldProtect, &oldProtect);
    return TRUE;
}

LPVOID AllocateTargetMemory(SIZE_T size, DWORD protection)
{
    return VirtualAlloc(nullptr, size, MEM_COMMIT | MEM_RESERVE, protection);
}

BOOL FreeTargetMemory(LPVOID address)
{
    return VirtualFree(address, 0, MEM_RELEASE);
}

BOOL ProtectTargetMemory(DWORD_PTR address, SIZE_T size, DWORD newProtection, DWORD* oldProtection)
{
    return VirtualProtect(reinterpret_cast<LPVOID>(address), size, newProtection, oldProtection);
}

// 处理命令
void ProcessCommand(const Command& cmd, Response& response)
{
    ZeroMemory(&response, sizeof(Response));
    
    switch (cmd.type)
    {
    case CMD_READ_MEMORY:
        {
            response.success = ReadTargetMemory(cmd.address, response.data, cmd.size);
            if (response.success)
            {
                response.dataSize = cmd.size;
            }
            else
            {
                response.errorCode = GetLastError();
            }
        }
        break;

    case CMD_WRITE_MEMORY:
        {
            response.success = WriteTargetMemory(cmd.address, cmd.data, cmd.size);
            if (!response.success)
            {
                response.errorCode = GetLastError();
            }
        }
        break;

    case CMD_PROTECT_MEMORY:
        {
            DWORD oldProtect;
            response.success = ProtectTargetMemory(cmd.address, cmd.size, cmd.protection, &oldProtect);
            if (response.success)
            {
                *reinterpret_cast<DWORD*>(response.data) = oldProtect;
                response.dataSize = sizeof(DWORD);
            }
            else
            {
                response.errorCode = GetLastError();
            }
        }
        break;

    case CMD_ALLOCATE_MEMORY:
        {
            LPVOID allocatedAddr = AllocateTargetMemory(cmd.size, cmd.protection);
            response.success = (allocatedAddr != nullptr);
            if (response.success)
            {
                *reinterpret_cast<DWORD_PTR*>(response.data) = reinterpret_cast<DWORD_PTR>(allocatedAddr);
                response.dataSize = sizeof(DWORD_PTR);
            }
            else
            {
                response.errorCode = GetLastError();
            }
        }
        break;

    case CMD_FREE_MEMORY:
        {
            response.success = FreeTargetMemory(reinterpret_cast<LPVOID>(cmd.address));
            if (!response.success)
            {
                response.errorCode = GetLastError();
            }
        }
        break;

    default:
        response.success = FALSE;
        response.errorCode = ERROR_INVALID_PARAMETER;
        break;
    }
}

// 通信线程
void CommunicationThread()
{
    while (g_bRunning)
    {
        // 创建命名管道
        g_hPipe = CreateNamedPipeW(
            PIPE_NAME,
            PIPE_ACCESS_DUPLEX,
            PIPE_TYPE_MESSAGE | PIPE_READMODE_MESSAGE | PIPE_WAIT,
            1,
            BUFFER_SIZE,
            BUFFER_SIZE,
            0,
            nullptr
        );

        if (g_hPipe == INVALID_HANDLE_VALUE)
        {
            Sleep(1000);
            continue;
        }

        // 等待客户端连接
        if (ConnectNamedPipe(g_hPipe, nullptr) || GetLastError() == ERROR_PIPE_CONNECTED)
        {
            // 处理客户端请求
            while (g_bRunning)
            {
                Command cmd;
                DWORD bytesRead;

                if (!ReadFile(g_hPipe, &cmd, sizeof(Command), &bytesRead, nullptr))
                {
                    break;
                }

                if (bytesRead != sizeof(Command))
                {
                    break;
                }

                Response response;
                ProcessCommand(cmd, response);

                DWORD bytesWritten;
                if (!WriteFile(g_hPipe, &response, sizeof(Response), &bytesWritten, nullptr))
                {
                    break;
                }
            }
        }

        CloseHandle(g_hPipe);
        g_hPipe = INVALID_HANDLE_VALUE;
    }
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            // 禁用DLL线程通知
            DisableThreadLibraryCalls(hModule);
            
            // 启动通信线程
            g_bRunning = true;
            g_communicationThread = std::thread(CommunicationThread);
            
            // 创建调试输出文件（可选）
            #ifdef _DEBUG
            AllocConsole();
            freopen_s(reinterpret_cast<FILE**>(stdout), "CONOUT$", "w", stdout);
            freopen_s(reinterpret_cast<FILE**>(stderr), "CONOUT$", "w", stderr);
            freopen_s(reinterpret_cast<FILE**>(stdin), "CONIN$", "r", stdin);
            std::cout << "[Payload] DLL已成功注入到进程 PID: " << GetCurrentProcessId() << std::endl;
            std::cout << "[Payload] 通信管道已启动: " << PIPE_NAME << std::endl;
            #endif
        }
        break;

    case DLL_PROCESS_DETACH:
        {
            // 停止通信线程
            g_bRunning = false;
            
            if (g_hPipe != INVALID_HANDLE_VALUE)
            {
                CloseHandle(g_hPipe);
                g_hPipe = INVALID_HANDLE_VALUE;
            }
            
            if (g_communicationThread.joinable())
            {
                g_communicationThread.join();
            }
            
            #ifdef _DEBUG
            std::cout << "[Payload] DLL正在卸载..." << std::endl;
            FreeConsole();
            #endif
        }
        break;
    }
    return TRUE;
}