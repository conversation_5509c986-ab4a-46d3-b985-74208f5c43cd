@echo off
chcp 65001 >nul
echo [+] 使用Visual Studio 2022编译无模块DLL注入工具
echo ================================

:: 设置Visual Studio路径
set VS_PATH=F:\Visual Studio2022
set MSVC_VERSION=14.44.35207
set MSVC_PATH=%VS_PATH%\VC\Tools\MSVC\%MSVC_VERSION%

:: 检查Visual Studio安装
if not exist "%MSVC_PATH%\bin\Hostx64\x64\cl.exe" (
    echo [-] 未找到Visual Studio 2022编译器
    echo [!] 请检查安装路径: %VS_PATH%
    pause
    exit /b 1
)

echo [+] 找到Visual Studio 2022编译器
echo [+] MSVC版本: %MSVC_VERSION%

:: 设置编译环境变量
echo [+] 设置编译环境...

:: 设置PATH
set PATH=%MSVC_PATH%\bin\Hostx64\x64;%PATH%
set PATH=%VS_PATH%\Common7\IDE;%PATH%

:: 设置包含目录
set INCLUDE=%MSVC_PATH%\include;%INCLUDE%
set INCLUDE=%VS_PATH%\SDK\NETFXSDK\4.8\include\um;%INCLUDE%

:: 设置库目录
set LIB=%MSVC_PATH%\lib\x64;%LIB%
set LIB=%VS_PATH%\SDK\NETFXSDK\4.8\lib\um\x64;%LIB%

:: 检查Windows SDK
if exist "C:\Program Files (x86)\Windows Kits\10" (
    echo [+] 找到Windows 10 SDK
    for /f "tokens=*" %%i in ('dir "C:\Program Files (x86)\Windows Kits\10\Include" /b /ad /o-n') do (
        set SDK_VERSION=%%i
        goto :sdk_found
    )
    :sdk_found
    echo [+] SDK版本: %SDK_VERSION%
    set INCLUDE=C:\Program Files (x86)\Windows Kits\10\Include\%SDK_VERSION%\um;%INCLUDE%
    set INCLUDE=C:\Program Files (x86)\Windows Kits\10\Include\%SDK_VERSION%\shared;%INCLUDE%
    set INCLUDE=C:\Program Files (x86)\Windows Kits\10\Include\%SDK_VERSION%\ucrt;%INCLUDE%
    set LIB=C:\Program Files (x86)\Windows Kits\10\Lib\%SDK_VERSION%\um\x64;%LIB%
    set LIB=C:\Program Files (x86)\Windows Kits\10\Lib\%SDK_VERSION%\ucrt\x64;%LIB%
)

:: 创建输出目录
if not exist "bin" mkdir bin

echo [+] 开始编译...

echo [+] 编译测试目标...
cl /EHsc /O2 /MT /Fe:bin\TestTarget.exe test_target.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 测试目标编译失败
    goto :cleanup
)

echo [+] 编译被注入DLL...
cl /EHsc /O2 /MT /LD /Fe:bin\Payload.dll Payload.cpp /link kernel32.lib user32.lib
if %errorlevel% neq 0 (
    echo [-] DLL编译失败
    goto :cleanup
)

echo [+] 编译控制器...
cl /EHsc /O2 /MT /Fe:bin\Controller.exe Controller.cpp /link kernel32.lib user32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 控制器编译失败
    goto :cleanup
)

echo [+] 编译注入器...
cl /EHsc /O2 /MT /Fe:bin\Injector.exe Injector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE
if %errorlevel% neq 0 (
    echo [-] 注入器编译失败，尝试编译简化版本...
    cl /EHsc /O2 /MT /Fe:bin\SimpleInjector.exe SimpleInjector.cpp ManualMapper.cpp /link kernel32.lib user32.lib advapi32.lib /SUBSYSTEM:CONSOLE
    if %errorlevel% neq 0 (
        echo [-] 简化注入器编译也失败
        goto :cleanup
    )
    echo [+] 简化注入器编译成功
)

:cleanup
:: 清理临时文件
del *.obj >nul 2>&1
del *.exp >nul 2>&1
del *.lib >nul 2>&1
del *.pdb >nul 2>&1

echo [+] 编译完成！
echo [+] 输出文件位于 bin\ 目录
echo.
dir bin
echo.
echo 使用方法:
echo   1. bin\TestTarget.exe
echo   2. bin\Injector.exe ^<进程名^> bin\Payload.dll
echo   3. bin\Controller.exe
echo.
pause
