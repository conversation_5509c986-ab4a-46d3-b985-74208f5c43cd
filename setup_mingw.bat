@echo off
chcp 65001 >nul
echo [+] MinGW-w64 环境配置脚本
echo ================================

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [-] 需要管理员权限来修改系统环境变量
    echo [!] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置下载目录
set MINGW_DIR=C:\mingw64
set DOWNLOAD_DIR=%TEMP%\mingw_setup

echo [+] 创建目录...
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"

echo [+] 下载预编译的MinGW-w64...
echo [!] 正在下载，请稍候...

:: 使用PowerShell下载预编译版本
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/niXman/mingw-builds-binaries/releases/download/13.2.0-rt_v11-rev0/winlibs-x86_64-posix-seh-gcc-13.2.0-mingw-w64msvcrt-11.0.1-r0.7z' -OutFile '%DOWNLOAD_DIR%\mingw.7z'}"

if not exist "%DOWNLOAD_DIR%\mingw.7z" (
    echo [-] 下载失败，尝试备用下载源...
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://sourceforge.net/projects/mingw-w64/files/Toolchains%%20targetting%%20Win64/Personal%%20Builds/mingw-builds/8.1.0/threads-posix/seh/x86_64-8.1.0-release-posix-seh-rt_v6-rev0.7z/download' -OutFile '%DOWNLOAD_DIR%\mingw.7z'}"
)

if not exist "%DOWNLOAD_DIR%\mingw.7z" (
    echo [-] 下载失败！
    echo [!] 请手动下载MinGW-w64预编译版本
    echo [!] 下载地址: https://www.mingw-w64.org/downloads/
    pause
    exit /b 1
)

echo [+] 下载完成，正在解压...

:: 检查是否有7zip
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    7z x "%DOWNLOAD_DIR%\mingw.7z" -o"C:\" -y
) else (
    :: 使用PowerShell解压
    powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%DOWNLOAD_DIR%\mingw.7z', 'C:\')}"
)

:: 检查解压结果
if not exist "%MINGW_DIR%\bin\gcc.exe" (
    echo [-] 解压失败或文件结构不正确
    echo [!] 请手动解压mingw.7z到C:\mingw64
    pause
    exit /b 1
)

echo [+] 解压完成！

echo [+] 配置环境变量...
:: 添加到系统PATH
setx PATH "%PATH%;%MINGW_DIR%\bin" /M

echo [+] 验证安装...
"%MINGW_DIR%\bin\gcc.exe" --version

echo [+] MinGW-w64 配置完成！
echo [+] 安装位置: %MINGW_DIR%
echo [+] 请重新打开命令行窗口以使用新的环境变量
echo.
echo 测试命令:
echo   gcc --version
echo   g++ --version
echo.
pause
